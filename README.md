# Clova API & Naver News Search Test Project

이 프로젝트는 네이버 클로바 챗봇 API, 네이버 뉴스 검색 API, 그리고 **Clova Studio RAG Reasoning**을 결합한 지능형 뉴스 검색 시스템을 테스트하기 위한 환경을 설정합니다.

## 🚀 주요 기능

### 1. 기본 네이버 뉴스 검색
- 키워드 기반 뉴스 검색
- 정확도순/최신순 정렬
- HTML 태그 제거 및 깔끔한 출력

### 2. **🤖 RAG Reasoning + 뉴스 검색 (NEW!)**
- **비정형적 검색 쿼리 지원**: "요즘 AI 관련해서 어떤 일들이 일어나고 있어?"
- **자동 쿼리 정제**: Clova Studio RAG reasoning으로 검색어 최적화
- **검색 의도 파악**: 사용자의 검색 목적 자동 분석
- **자연어 대화형 검색**: 일상 언어로 뉴스 검색 가능

### 3. Node.js 챗봇 이식 지원
- JavaScript 버전 예시 코드 제공
- 챗봇 통합을 위한 간단한 API 함수

## 사전 준비

1. **네이버 개발자 센터**에서 애플리케이션을 등록하고 다음 정보를 확인하세요:
   - 네이버 검색 API 클라이언트 ID (NAVER_CLIENT_ID)
   - 네이버 검색 API 클라이언트 시크릿 (NAVER_CLIENT_SECRET)

2. **Clova Studio**에서 API 키를 발급받으세요:
   - Clova Studio API 키 (CLOVA_STUDIO_API_KEY)
   - [Clova Studio 콘솔](https://clovastudio.ncloud.com/)에서 발급

3. **네이버 클라우드 플랫폼**에서 다음 정보를 확인하세요 (클로바 챗봇용):
   - Access Key (NCP_ACCESS_KEY)
   - Secret Key (NCP_SECRET_KEY)
   - API Gateway Invoke URL (APIGW_INVOKE_URL)

## 환경 설정

1. 가상환경 활성화 (Windows)
   ```
   .\venv\Scripts\activate
   ```

2. 필요한 패키지 설치
   ```
   pip install -r requirements.txt
   ```

3. `.env` 파일 설정
   - `.env` 파일을 열고 다음 정보를 입력하세요:
     ```
     # 네이버 검색 API 인증 정보 (필수)
     NAVER_CLIENT_ID=발급받은_클라이언트_ID
     NAVER_CLIENT_SECRET=발급받은_클라이언트_시크릿

     # Clova Studio API 키 (RAG Reasoning용, 필수)
     CLOVA_STUDIO_API_KEY=발급받은_클로바_스튜디오_API_키

     # 네이버 클라우드 플랫폼 인증 정보 (클로바 챗봇용)
     NCP_ACCESS_KEY=발급받은_Access_Key
     NCP_SECRET_KEY=발급받은_Secret_Key

     # API Gateway Invoke URL (클로바 챗봇용)
     APIGW_INVOKE_URL=발급받은_API_Gateway_URL

     # 선택사항: 대화 추적을 위한 사용자 ID
     USER_ID=test_user
     ```

## 사용 방법

### 1. **🤖 RAG Reasoning + 뉴스 검색 (추천!)**

#### 지능형 뉴스 검색 실행:
```bash
python rag_news_search.py
```

#### 특징:
- **자연어 검색**: "요즘 AI 관련해서 어떤 일들이 일어나고 있어?"
- **자동 쿼리 정제**: "AI 관련 최신 동향" 으로 자동 변환
- **검색 의도 파악**: 최신뉴스, 분석정보, 특정사건 등 자동 분류
- **대화형 모드**: 자연스러운 대화로 뉴스 검색

#### 사용 예시:
```
🔍 원본 검색 쿼리: '요즘 블록체인 기술이 어떻게 발전하고 있는지 궁금해'
📝 검색 쿼리 정제 중...
✨ 정제된 검색어: '블록체인 기술 발전 동향'
🎯 검색 의도: 최신뉴스
📰 뉴스 검색 중...
✅ 검색 완료! (총 6244개 발견)
```

### 2. 기본 네이버 뉴스 검색 API 테스트

#### 간단한 테스트:
```bash
python simple_news_test.py
```

#### 전체 기능 테스트 (대화형 모드 포함):
```bash
python naver_news_search.py
```

#### 주요 기능:
- **뉴스 검색**: 키워드로 네이버 뉴스 검색
- **정렬 옵션**: 정확도순(sim) 또는 최신순(date)
- **결과 개수 조절**: 1~100개까지 설정 가능
- **HTML 태그 제거**: 깔끔한 텍스트 출력
- **대화형 모드**: 실시간 검색어 입력
- **Node.js 이식용 JSON**: 챗봇 개발을 위한 구조화된 데이터

### 3. 클로바 챗봇 테스트

#### 챗봇 테스트 실행:
```bash
python test_clova_api.py
```

#### 대화 시작:
- 화면에 표시되는 프롬프트에 메시지를 입력하면 챗봇이 응답합니다.
- 종료하려면 '종료', 'exit', 또는 'quit'을 입력하세요.

### 4. Node.js 챗봇 이식

#### 기본 뉴스 검색:
`naver_news_nodejs_example.js` 파일에 Node.js용 예시 코드가 포함되어 있습니다:

```javascript
const { NaverNewsSearcher, getNewsForChatbot } = require('./naver_news_nodejs_example.js');

// 챗봇에서 사용 예시
const result = await getNewsForChatbot("사용자 메시지");
if (result.success) {
    console.log(result.message);
    result.items.forEach(item => {
        console.log(`제목: ${item.title}`);
        console.log(`요약: ${item.description}`);
    });
}
```

#### **RAG + 뉴스 검색 (추천!):**
`rag_news_search_nodejs.js` 파일에 지능형 검색 시스템이 포함되어 있습니다:

```javascript
const { getIntelligentNewsForChatbot } = require('./rag_news_search_nodejs.js');

// 자연어 검색 예시
const result = await getIntelligentNewsForChatbot("요즘 AI 기술이 어떻게 발전하고 있어?");
if (result.success) {
    console.log(`✨ 정제된 검색어: ${result.refined_query}`);
    console.log(`🎯 검색 의도: ${result.search_intent}`);
    console.log(result.message);

    result.items.forEach(item => {
        console.log(`📰 ${item.title}`);
        console.log(`📝 ${item.description}`);
    });
}
```

#### Node.js 패키지 설치:
```bash
npm install axios dotenv uuid
# 또는
npm install  # package.json이 있는 경우
```

## API 문서

### Clova Studio RAG Reasoning
- [Clova Studio 콘솔](https://clovastudio.ncloud.com/)
- [Clova Studio API 문서](https://api.ncloud-docs.com/docs/ai-naver-clovastudio-summary)
- [RAG Reasoning 가이드](https://guide.ncloud-docs.com/docs/clovastudio-releasenote)

### 네이버 검색 API
- [네이버 검색 API 뉴스 검색](https://developers.naver.com/docs/serviceapi/search/news/news.md)
- [네이버 검색 API 블로그 검색 구현 예제](https://developers.naver.com/docs/serviceapi/search/blog/blog.md#%EA%B2%80%EC%83%89-api-%EB%B8%94%EB%A1%9C%EA%B7%B8-%EA%B2%80%EC%83%89-%EA%B5%AC%ED%98%84-%EC%98%88%EC%A0%9C)
- [네이버 개발자 센터](https://developers.naver.com/)

### 클로바 챗봇 API
- [Clova Chatbot API 가이드](https://api.ncloud-docs.com/docs/ai-application-service-chatbot-chatbot)
- [Clova Chatbot 콘솔](https://chatbot.ncloud.com/)

## 문제 해결

### RAG Reasoning + 뉴스 검색
- **Clova Studio API 키 오류**: `CLOVA_STUDIO_API_KEY`가 올바르게 설정되었는지 확인
- **RAG reasoning 실패**: 원본 검색어로 대체되어 검색 진행
- **검색 쿼리 정제 안됨**: 네트워크 문제 또는 API 한도 확인

### 네이버 뉴스 검색 API
- **인증 오류**: `NAVER_CLIENT_ID`와 `NAVER_CLIENT_SECRET`이 올바르게 설정되었는지 확인
- **검색 결과 없음**: 검색어를 다르게 시도하거나 더 일반적인 키워드 사용
- **요청 한도 초과**: 네이버 개발자 센터에서 API 사용량 확인

### 클로바 챗봇 API
- **API 키 오류**: API 키가 유효하지 않으면 인증 오류가 발생합니다.
- **챗봇 ID 오류**: 챗봇 ID가 올바르지 않으면 404 오류가 발생합니다.
- **네트워크 문제**: 인터넷 연결을 확인하세요.

## 주의사항

- `.env` 파일은 절대 버전 관리 시스템에 올리지 마세요.
- API 키와 시크릿 키는 안전하게 보관하세요.
- 무료 사용 한도를 초과하면 추가 요금이 부과될 수 있습니다.
- 네이버 검색 API는 일일 25,000회 호출 제한이 있습니다.
- Clova Studio API는 사용량에 따라 과금될 수 있습니다.

## 파일 구조

```
clova-api-test/
├── .env                           # 환경변수 설정 파일
├── requirements.txt               # Python 패키지 의존성
├── README.md                      # 프로젝트 설명서
├── test_clova_api.py             # 클로바 챗봇 API 테스트
├── test_clova_api copy.py        # 기존 뉴스 검색 예시
├── naver_news_search.py          # 네이버 뉴스 검색 전체 기능
├── simple_news_test.py           # 간단한 뉴스 검색 테스트
├── naver_news_nodejs_example.js  # Node.js 이식용 예시 코드
└── venv/                         # Python 가상환경
```

## 개발 계획

1. **Python 테스트 완료** ✅
   - 네이버 뉴스 검색 API 연동
   - 다양한 검색 옵션 구현
   - 에러 처리 및 예외 상황 대응

2. **Node.js 챗봇 이식** 🔄
   - `naver_news_nodejs_example.js` 참고
   - Express.js 또는 다른 프레임워크와 통합
   - 챗봇 대화 흐름에 뉴스 검색 기능 추가

3. **추가 기능 고려사항**
   - 뉴스 카테고리별 검색
   - 검색 결과 캐싱
   - 사용자 선호도 기반 뉴스 추천
