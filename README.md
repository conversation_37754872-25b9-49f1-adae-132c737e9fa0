# Clova API & Naver News Search Test Project

이 프로젝트는 네이버 클로바 챗봇 API와 네이버 뉴스 검색 API를 테스트하기 위한 환경을 설정합니다.

## 사전 준비

1. 네이버 클라우드 플랫폼 계정이 필요합니다.
2. 네이버 개발자 센터에서 애플리케이션을 등록하고 다음 정보를 확인하세요:
   - 네이버 검색 API 클라이언트 ID (NAVER_CLIENT_ID)
   - 네이버 검색 API 클라이언트 시크릿 (NAVER_CLIENT_SECRET)
3. 네이버 클라우드 플랫폼에서 다음 정보를 확인하세요 (클로바 챗봇용):
   - Access Key (NCP_ACCESS_KEY)
   - Secret Key (NCP_SECRET_KEY)
   - API Gateway Invoke URL (APIGW_INVOKE_URL)

## 환경 설정

1. 가상환경 활성화 (Windows)
   ```
   .\venv\Scripts\activate
   ```

2. 필요한 패키지 설치
   ```
   pip install -r requirements.txt
   ```

3. `.env` 파일 설정
   - `.env` 파일을 열고 다음 정보를 입력하세요:
     ```
     # 네이버 검색 API 인증 정보 (필수)
     NAVER_CLIENT_ID=발급받은_클라이언트_ID
     NAVER_CLIENT_SECRET=발급받은_클라이언트_시크릿

     # 네이버 클라우드 플랫폼 인증 정보 (클로바 챗봇용)
     NCP_ACCESS_KEY=발급받은_Access_Key
     NCP_SECRET_KEY=발급받은_Secret_Key

     # API Gateway Invoke URL (클로바 챗봇용)
     APIGW_INVOKE_URL=발급받은_API_Gateway_URL

     # 선택사항: 대화 추적을 위한 사용자 ID
     USER_ID=test_user
     ```

## 사용 방법

### 1. 네이버 뉴스 검색 API 테스트

#### 간단한 테스트:
```bash
python simple_news_test.py
```

#### 전체 기능 테스트 (대화형 모드 포함):
```bash
python naver_news_search.py
```

#### 주요 기능:
- **뉴스 검색**: 키워드로 네이버 뉴스 검색
- **정렬 옵션**: 정확도순(sim) 또는 최신순(date)
- **결과 개수 조절**: 1~100개까지 설정 가능
- **HTML 태그 제거**: 깔끔한 텍스트 출력
- **대화형 모드**: 실시간 검색어 입력
- **Node.js 이식용 JSON**: 챗봇 개발을 위한 구조화된 데이터

### 2. 클로바 챗봇 테스트

#### 챗봇 테스트 실행:
```bash
python test_clova_api.py
```

#### 대화 시작:
- 화면에 표시되는 프롬프트에 메시지를 입력하면 챗봇이 응답합니다.
- 종료하려면 '종료', 'exit', 또는 'quit'을 입력하세요.

### 3. Node.js 챗봇 이식

`naver_news_nodejs_example.js` 파일에 Node.js용 예시 코드가 포함되어 있습니다:

```javascript
const { NaverNewsSearcher, getNewsForChatbot } = require('./naver_news_nodejs_example.js');

// 챗봇에서 사용 예시
const result = await getNewsForChatbot("사용자 메시지");
if (result.success) {
    console.log(result.message);
    result.items.forEach(item => {
        console.log(`제목: ${item.title}`);
        console.log(`요약: ${item.description}`);
    });
}
```

## API 문서

### 네이버 검색 API
- [네이버 검색 API 뉴스 검색](https://developers.naver.com/docs/serviceapi/search/news/news.md)
- [네이버 검색 API 블로그 검색 구현 예제](https://developers.naver.com/docs/serviceapi/search/blog/blog.md#%EA%B2%80%EC%83%89-api-%EB%B8%94%EB%A1%9C%EA%B7%B8-%EA%B2%80%EC%83%89-%EA%B5%AC%ED%98%84-%EC%98%88%EC%A0%9C)
- [네이버 개발자 센터](https://developers.naver.com/)

### 클로바 챗봇 API
- [Clova Chatbot API 가이드](https://api.ncloud-docs.com/docs/ai-application-service-chatbot-chatbot)
- [Clova Chatbot 콘솔](https://chatbot.ncloud.com/)

## 문제 해결

### 네이버 뉴스 검색 API
- **인증 오류**: `NAVER_CLIENT_ID`와 `NAVER_CLIENT_SECRET`이 올바르게 설정되었는지 확인
- **검색 결과 없음**: 검색어를 다르게 시도하거나 더 일반적인 키워드 사용
- **요청 한도 초과**: 네이버 개발자 센터에서 API 사용량 확인

### 클로바 챗봇 API
- **API 키 오류**: API 키가 유효하지 않으면 인증 오류가 발생합니다.
- **챗봇 ID 오류**: 챗봇 ID가 올바르지 않으면 404 오류가 발생합니다.
- **네트워크 문제**: 인터넷 연결을 확인하세요.

## 주의사항

- `.env` 파일은 절대 버전 관리 시스템에 올리지 마세요.
- API 키와 시크릿 키는 안전하게 보관하세요.
- 무료 사용 한도를 초과하면 추가 요금이 부과될 수 있습니다.
- 네이버 검색 API는 일일 25,000회 호출 제한이 있습니다.

## 파일 구조

```
clova-api-test/
├── .env                           # 환경변수 설정 파일
├── requirements.txt               # Python 패키지 의존성
├── README.md                      # 프로젝트 설명서
├── test_clova_api.py             # 클로바 챗봇 API 테스트
├── test_clova_api copy.py        # 기존 뉴스 검색 예시
├── naver_news_search.py          # 네이버 뉴스 검색 전체 기능
├── simple_news_test.py           # 간단한 뉴스 검색 테스트
├── naver_news_nodejs_example.js  # Node.js 이식용 예시 코드
└── venv/                         # Python 가상환경
```

## 개발 계획

1. **Python 테스트 완료** ✅
   - 네이버 뉴스 검색 API 연동
   - 다양한 검색 옵션 구현
   - 에러 처리 및 예외 상황 대응

2. **Node.js 챗봇 이식** 🔄
   - `naver_news_nodejs_example.js` 참고
   - Express.js 또는 다른 프레임워크와 통합
   - 챗봇 대화 흐름에 뉴스 검색 기능 추가

3. **추가 기능 고려사항**
   - 뉴스 카테고리별 검색
   - 검색 결과 캐싱
   - 사용자 선호도 기반 뉴스 추천
