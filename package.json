{"name": "naver-news-search-chatbot", "version": "1.0.0", "description": "네이버 뉴스 검색 API를 활용한 Node.js 챗봇 예시", "main": "naver_news_nodejs_example.js", "scripts": {"start": "node naver_news_nodejs_example.js", "test": "node naver_news_nodejs_example.js"}, "keywords": ["naver", "news", "search", "api", "chatbot", "nodejs"], "author": "Your Name", "license": "MIT", "dependencies": {"axios": "^1.6.0", "dotenv": "^16.3.0"}, "devDependencies": {"nodemon": "^3.0.0"}, "engines": {"node": ">=14.0.0"}}