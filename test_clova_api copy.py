# rag reasoning example

import http.client
import json


class CompletionExecutor:
    def __init__(self, host, api_key, request_id):
        self._host = host
        self._api_key = api_key
        self._request_id = request_id

    def _send_request(self, completion_request):
        headers = {
            'Content-Type': 'application/json; charset=utf-8',
            'Authorization': self._api_key,
            'X-NCP-CLOVASTUDIO-REQUEST-ID': self._request_id
        }

        conn = http.client.HTTPSConnection(self._host)
        conn.request('POST', '/v1/api-tools/rag-reasoning', json.dumps(completion_request), headers)
        response = conn.getresponse()
        result = json.loads(response.read().decode(encoding='utf-8'))
        conn.close()
        return result

    def execute(self, completion_request):
        res = self._send_request(completion_request)
        if res['status']['code'] == '20000':
            return res['result']
        else:
            return 'Error'


if __name__ == '__main__':
    completion_executor = CompletionExecutor(
        host='clovastudio.stream.ntruss.com',
        api_key='Bearer <api-key>',
        request_id='b9d36b4dd2174a9dbc63ef30ed1bd61c'
    )

    request_data = json.loads("""{
  "messages" : [ {
    "content" : "VPC 삭제 방법 알려줘",
    "role" : "user"
  } ],
  "tools" : [ {
    "function" : {
      "description" : "NCloud 관련 검색을 할 때 사용하는 도구입니다.\n나누어 질문해야 하는 경우 쿼리를 쪼개 나누어서 도구를 사용합니다.\n정보를 찾을 수 없었던 경우, 최종 답을 하지 않고 suggested_queries를 참고하여 도구를 다시 사용할 수 있습니다.",
      "name" : "ncloud_cs_retrieval",
      "parameters" : {
        "type" : "object",
        "properties" : {
          "query" : {
            "type" : "string",
            "description" : "사용자의 검색어를 정제해서 넣으세요."
          }
        },
        "required" : [ "query" ]
      }
    }
  } ],
  "toolChoice" : "auto",
  "topP" : 0.8,
  "topK" : 0,
  "maxTokens" : 1024,
  "temperature" : 0.5,
  "repetitionPenalty" : 1.1,
  "stop" : [ ],
  "seed" : 0,
  "includeAiFilters" : false
}""", strict=False)

    response_text = completion_executor.execute(request_data)
    print(request_data)
    print(response_text)

#-------------------------
# 변수 설명


'''
필드	필수 여부	설명
Content-Type
Y	
Application/json
Authorization
Y	
Bearer <api-key>
X-NCP-CLOVASTUDIO-REQUEST-ID
N	
각 요청에 대한 REQUEST ID
바디
필드	타입	필수 여부	기본값	설명
messages
array[ChatMessage]	Y	-	
대화 메시지 목록. 사용자의 요청과 관련된 모든 메시지를 포함합니다.
ChatMessage화살표
ChatMessage.role
enum	Y	-	
대화 메시지 역할

- system: 역할을 규정하는 지시문
- user: 사용자의 발화/질문
- assistant : 사용자의 발화/질문에 대한 답변
- tool: assistant가 호출한 검색 데이터를 가져오고 그 결과를 제공하는 메시지
ChatMessage.content
string	Y	-	
대화 메시지 내용
ChatMessage.toolCallId
string	N	-	
도구 아이디

- role이 tool인 경우, 필수 입력
- assistant의 toolCalls 요청과 연결하는 용도
ChatMessage.toolCalls
array[ToolCall]	N	-	
Assistant의 호출 도구 정보

- role이 tool인 경우 assistant의 toolCalls 요청과 같이 입력
ToolCall화살표
ToolCall.id
string	N	-	
각 함수 호출의 고유 식별자입니다.

- role이 tool인 경우, 필수 입력 
- assistant의 toolCalls 요청과 연결하는 용도
ToolCall.type
string	N	-	
현재 사용 가능한 도구 타입은 function 입니다.
ToolCall.function
Function	N	-	
호출할 검색 함수에 대한 정보입니다.
tools
array[Tool]	Y	-	
사용 가능한 도구 목록입니다. 선택한 도구와 관련된 정보가 포함됩니다.
Tool화살표
Tool.type
string	N	function	
현재 사용 가능한 도구 타입은 function입니다.
Tool.function
Function	Y	-	
호출할 검색 함수의 정보입니다. 각 도구의 name과 parameters를 포함합니다.
Function화살표
Function.description
string	Y	-	
검색 함수의 기능, 사용 시기, 동작 방식에 대한 자세한 설명
Function.name
string	Y	-	
검색 함수의 이름
Function.parameters
object	Y	-	
검색 함수가 사용될 때 전달되는 매개변수입니다. properties와 required 필드를 포함합니다.
toolChoice
enum | object	N	auto	
함수 호출 동작 방식 설정

- auto: 모델이 자동으로 함수를 호출하도록 설정
- none: 모델이 함수를 호출하지 않도록 설정
- {"type": "function", "function": {"name": "my_function}}: 모델이 특정 함수를 강제로 호출하도록 설정
topP
double	N	0.8	
생성 토큰 후보군을 누적 분포를 기반으로 샘플링 (기본값: 0.8)

- 0 < topP ≦ 1
topK
integer	N	0	
생성 토큰 후보군에서 확률이 높은 토큰 top_k를 지정하여 샘플링 (기본값: 0)

- 0 ≦ topK ≦ 128
maxTokens
integer	N	1024	
최대 생성 토큰 수
temperature
double	N	0.5	
생성 토큰에 대한 다양성 정도: 높을수록 다양한 문장 생성 (기본값: 0.5)

- 0.00 < temperature ≦ 1.00
repetitionPenalty
double	N	1.1	
같은 토큰을 생성하는 것에 대한 패널티 정도 (기본값: 1.1)

- 0.0 < repetitionPenalty ≦ 2.0
stop
array[string]	N	[]	
토큰 생성 중단 문자

- [](기본값)
seed
integer	N	0	
모델 반복 실행 시 결괏값의 일관성 수준 조정

- 0: 일관성 수준 랜덤 적용 (기본값)
- 1 ≤ seed ≤ 4294967295: 일관되게 생성하고자 하는 결괏값의 seed 값 또는 사용자가 지정하고자 하는 seed 값
includeAiFilters
boolean	N	true	
AI 필터(생성된 결괏값에 대해 욕설, 비하/차별/혐오, 성희롱/음란 등 카테고리별로 해당하는 정도) 결과 표시 여부 (기본값: true)

- true: 표시
- false: 표시 안 함

필드	타입	필수 여부	기본값	설명
message
ChatMessage	Y	-	
대화 메시지 목록
ChatMessage화살표
ChatMessage.role
enum	Y	-	
대화 메시지 역할

- system: 역할을 규정하는 지시문
- user: 사용자의 발화/질문
- assistant : 사용자의 발화/질문에 대한 답변
- tool: assistant가 호출한 검색 데이터를 가져오고 그 결과를 제공하는 메시지
ChatMessage.content
string	Y	-	
대화 메시지 내용
ChatMessage.thinkingContent
string	N	-	
호출 과정 속 모델의 의사결정 흐름을 보여줍니다.
ChatMessage.toolCalls
array[ToolCall]	Y	-	
assistant의 호출 도구 정보

- role이 tool인 경우 assistant의 toolCalls 요청과 같이 입력
ToolCall화살표
ToolCall.id
string	Y	-	
각 함수 호출의 고유 식별자입니다.

- role이 tool인 경우, 필수 입력 
- assistant의 toolCalls 요청과 연결하는 용도
ToolCall.type
string	Y	-	
현재 사용 가능한 도구 타입은 function 입니다.
ToolCall.function
Function	Y	-	
호출할 함수에 대한 정보입니다.
Function화살표
Function.name
string	Y	-	
호출할 함수의 이름입니다.
Function.arguments
object	Y	-	
함수에 전달된 인자 값들이 포함됩니다.
usage
Usage	Y	-	
토큰 사용량
Usage화살표
Usage.completionTokens
integer	Y	-	
생성 토큰 수
Usage.promptTokens
integer	Y	-	
입력(프롬프트) 토큰 수
Usage.totalTokens
integer	Y	-	
전체 토큰 수



#바디 결과 예제

{
    "messages": [
        {
            "content": "VPC 삭제 방법 알려줘",
            "role": "user"
        }
    ],
    "tools": [
        {
            "function": {
                "description": "NCloud 관련 검색을 할 때 사용하는 도구입니다.\n나누어 질문해야 하는 경우 쿼리를 쪼개 나누어서 도구를 사용합니다.\n정보를 찾을 수 없었던 경우, 최종 답을 하지 않고 suggested_queries를 참고하여 도구를 다시 사용할 수 있습니다.",
                "name": "ncloud_cs_retrieval",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "사용자의 검색어를 정제해서 넣으세요."
                        }
                    },
                    "required": [
                        "query"
                    ]
                }
            }
        }
    ],
    "toolChoice": "auto",
    "topP": 0.8,
    "topK": 0,
    "maxTokens": 1024,
    "temperature": 0.5,
    "repetitionPenalty": 1.1,
    "stop": [],
    "seed": 0,
    "includeAiFilters": true
}


{
    "status": {
        "code": "20000",
        "message": "OK"
    },
    "result": {
        "message": {
            "content": "",
            "role": "assistant",
            "thinkingContent": "사용자가 VPC 삭제 방법에 대해 문의했습니다. 이를 해결하기 위해 NCloud 관련 검색 도구를 사용하여 VPC 삭제 방법에 대한 정보를 찾아야 합니다.",
            "toolCalls": [
                {
                    "id": "e1d6b6c1d7a54ac69b7b60f6d9a88da5",
                    "type": "function",
                    "function": {
                        "name": "ncloud_cs_retrieval",
                        "arguments": {
                            "query": "VPC 삭제 방법"
                        }
                    }
                }
            ]
        },
        "usage": {
            "promptTokens": 204,
            "completionTokens": 77,
            "totalTokens": 281
        }
    }
}

'''