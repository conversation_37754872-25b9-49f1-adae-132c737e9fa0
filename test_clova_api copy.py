#웹 검색 테스트

import os
import requests

# 1) 환경변수에서 클라이언트 아이디·시크릿 로드  
client_id = os.getenv("NAVER_CLIENT_ID")  
client_secret = os.getenv("NAVER_CLIENT_SECRET")  

# 2) 요청 헤더 설정  
headers = {  
    "X-Naver-Client-Id": client_id,  
    "X-Naver-Client-Secret": client_secret  
}  

# 3) 쿼리 및 파라미터 설정  
params = {  
    "query": "경제 뉴스",  
    "display": 50,        # 최대 100  
    "start": 1,           # 조회 시작 위치  
    "sort": "date"        # 최신순 정렬  
}  

# 4) API 호출  
response = requests.get(  
    "https://openapi.naver.com/v1/search/news.json",  
    headers=headers,  
    params=params  
)  
items = response.json().get("items", [])  # 검색 결과 리스트  

print(items)