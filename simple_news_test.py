# 간단한 네이버 뉴스 검색 테스트
# Node.js 챗봇 이식을 위한 기본 기능 검증

import os
import requests
import json
from dotenv import load_dotenv

# .env 파일에서 환경변수 로드
load_dotenv()

def search_naver_news(query, count=5):
    """
    네이버 뉴스 검색 함수 (Node.js 이식용 간단 버전)
    
    Args:
        query (str): 검색어
        count (int): 검색 결과 개수 (기본값: 5)
    
    Returns:
        dict: 검색 결과
    """
    # API 키 확인
    client_id = os.getenv("NAVER_CLIENT_ID")
    client_secret = os.getenv("NAVER_CLIENT_SECRET")
    
    if not client_id or not client_secret:
        return {
            "success": False,
            "error": "API 키가 설정되지 않았습니다."
        }
    
    # 요청 헤더
    headers = {
        "X-Naver-Client-Id": client_id,
        "X-Naver-Client-Secret": client_secret
    }
    
    # 요청 파라미터
    params = {
        "query": query,
        "display": min(count, 100),  # 최대 100개
        "start": 1,
        "sort": "date"  # 최신순
    }
    
    try:
        # API 호출
        response = requests.get(
            "https://openapi.naver.com/v1/search/news.json",
            headers=headers,
            params=params,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            return {
                "success": True,
                "total": data.get("total", 0),
                "items": data.get("items", [])
            }
        else:
            return {
                "success": False,
                "error": f"HTTP {response.status_code}: {response.text}"
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

def clean_html_tags(text):
    """HTML 태그 제거"""
    import re
    clean = re.compile('<.*?>')
    return re.sub(clean, '', text)

def main():
    """테스트 실행"""
    print("🔍 네이버 뉴스 검색 API 테스트")
    print("=" * 40)
    
    # 테스트 검색어
    test_query = "최근 트럼프 정부의 악질적인 관세 정책"
    
    print(f"검색어: {test_query}")
    print("-" * 40)
    
    # 뉴스 검색
    result = search_naver_news(test_query, 3)
    
    if result["success"]:
        print(f"✅ 검색 성공! 총 {result['total']}개의 뉴스 발견")
        print()
        
        for i, item in enumerate(result["items"], 1):
            title = clean_html_tags(item.get("title", ""))
            description = clean_html_tags(item.get("description", ""))
            link = item.get("link", "")
            pub_date = item.get("pubDate", "")
            
            print(f"📰 뉴스 {i}")
            print(f"제목: {title}")
            print(f"요약: {description}")
            print(f"발행일: {pub_date}")
            print(f"링크: {link}")
            print("-" * 40)
    else:
        print(f"❌ 검색 실패: {result['error']}")
    
    # Node.js 이식을 위한 JSON 형태 출력
    print("\n🔧 Node.js 이식용 JSON 형태:")
    print(json.dumps(result, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    main()
